#!/bin/bash

# GEMINI BALANCE 轻量级管理脚本
# 修复版本 - 支持 Docker Compose V2

set -e

PROJECT_NAME="GEMINI BALANCE (轻量级)"
COMPOSE_FILE="docker-compose-lite.yaml"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker未运行，请先启动Docker服务"
        exit 1
    fi
}

# 显示帮助
show_help() {
    echo -e "${BLUE}$PROJECT_NAME 管理脚本${NC}"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  start     - 启动服务"
    echo "  stop      - 停止服务"
    echo "  restart   - 重启服务"
    echo "  status    - 查看服务状态"
    echo "  logs      - 查看日志"
    echo "  update    - 更新服务"
    echo "  stats     - 查看资源使用"
    echo "  backup    - 备份SQLite数据库"
    echo "  clean     - 清理Docker资源"
    echo "  help      - 显示帮助信息"
}

# 启动服务
start_service() {
    print_message "启动 $PROJECT_NAME 服务..."
    check_docker
    sudo docker compose -f "$COMPOSE_FILE" up -d
    print_message "服务启动完成！"
    sleep 3
    show_status
}

# 停止服务
stop_service() {
    print_message "停止 $PROJECT_NAME 服务..."
    sudo docker compose -f "$COMPOSE_FILE" down
    print_message "服务已停止"
}

# 重启服务
restart_service() {
    print_message "重启 $PROJECT_NAME 服务..."
    sudo docker compose -f "$COMPOSE_FILE" restart
    print_message "服务重启完成！"
    sleep 3
    show_status
}

# 查看状态
show_status() {
    print_message "服务状态:"
    sudo docker compose -f "$COMPOSE_FILE" ps
    echo ""

    # 检查应用健康状态
    if curl -f http://localhost:8000/health &>/dev/null; then
        print_message "✅ 应用运行正常"
        PUBLIC_IP=$(curl -s --connect-timeout 3 ifconfig.me 2>/dev/null || echo 'YOUR_SERVER_IP')
        echo "🌐 访问地址: http://$PUBLIC_IP:8000"
    else
        print_warning "⚠️  应用可能未完全启动"
    fi
}

# 查看日志
show_logs() {
    print_message "显示服务日志 (按 Ctrl+C 退出):"
    sudo docker compose -f "$COMPOSE_FILE" logs -f --tail=30
}

# 更新服务
update_service() {
    print_message "更新 $PROJECT_NAME 服务..."
    check_docker

    print_message "拉取最新镜像..."
    sudo docker pull ghcr.io/snailyp/gemini-balance:latest

    print_message "重启服务..."
    sudo docker compose -f "$COMPOSE_FILE" up -d

    print_message "清理旧镜像..."
    sudo docker image prune -f

    print_message "更新完成！"
    sleep 3
    show_status
}

# 查看资源使用
show_stats() {
    print_message "资源使用情况:"
    echo ""
    echo "系统资源:"
    free -h
    echo ""
    echo "Docker容器资源:"
    sudo docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
    echo ""
    echo "磁盘使用:"
    df -h . | tail -1
    echo ""
    echo "数据库文件大小:"
    if [ -f "./data/gemini_balance.db" ]; then
        ls -lh ./data/gemini_balance.db | awk '{print $5 " - " $9}'
    else
        echo "数据库文件不存在"
    fi
}

# 备份数据库
backup_database() {
    print_message "备份SQLite数据库..."

    BACKUP_DIR="./backups"
    mkdir -p "$BACKUP_DIR"

    if [ -f "./data/gemini_balance.db" ]; then
        BACKUP_FILE="$BACKUP_DIR/gemini_balance_$(date +%Y%m%d_%H%M%S).db"
        cp "./data/gemini_balance.db" "$BACKUP_FILE"
        print_message "数据库备份完成: $BACKUP_FILE"

        # 压缩备份文件
        gzip "$BACKUP_FILE"
        print_message "备份文件已压缩: ${BACKUP_FILE}.gz"
    else
        print_error "数据库文件不存在: ./data/gemini_balance.db"
    fi
}

# 清理系统
clean_system() {
    print_message "清理Docker资源..."
    
    print_message "清理停止的容器..."
    sudo docker container prune -f
    
    print_message "清理未使用的镜像..."
    sudo docker image prune -f
    
    print_message "清理未使用的网络..."
    sudo docker network prune -f
    
    # 清理旧的备份文件 (保留最近5个)
    if [ -d "./backups" ]; then
        print_message "清理旧备份文件..."
        cd ./backups
        ls -t *.gz 2>/dev/null | tail -n +6 | xargs rm -f 2>/dev/null || true
        cd ..
    fi
    
    print_message "清理完成！"
}

# 主逻辑
case "${1:-help}" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    update)
        update_service
        ;;
    stats)
        show_stats
        ;;
    backup)
        backup_database
        ;;
    clean)
        clean_system
        ;;
    help|*)
        show_help
        ;;
esac
