# GEMINI BALANCE 优化部署指南

## 快速部署（推荐）
1. 上传所有文件到服务器
2. 运行自动部署脚本：
   chmod +x deploy-optimized.sh
   ./deploy-optimized.sh

## 手动部署
1. 安装Docker：
   curl -fsSL https://get.docker.com | sudo bash -s docker --mirror Aliyun

2. 启动Docker服务：
   sudo systemctl start docker
   sudo systemctl enable docker

3. 安装Docker Compose：
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose

4. 启动项目：
   sudo docker-compose up -d

## 常用管理命令
- 查看状态：sudo docker-compose ps
- 查看日志：sudo docker-compose logs -f
- 重启服务：sudo docker-compose restart
- 停止服务：sudo docker-compose down
- 更新服务：sudo docker-compose pull && sudo docker-compose up -d

## 注意事项
1. 请修改.env文件中的API_KEYS和ALLOWED_TOKENS为您的真实值
2. 确保服务器8000端口已开放
3. 建议定期备份mysql_data卷中的数据

