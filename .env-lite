# 轻量级部署配置 - 使用SQLite数据库

# 数据库配置 (使用SQLite，无需MySQL)
DATABASE_TYPE=sqlite
SQLITE_DATABASE=data/gemini_balance.db

# API配置
API_KEYS=["AIzaSyDQ_jBZ18N2jkNMcpqzJIEvd5xWUfg5Se8","AIzaSyDruPntJPgynkzO6RynqwmbTEF8WJ7PrDU"]
ALLOWED_TOKENS=["sk-seaweep"]

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
LOG_LEVEL=WARNING
TIME_OUT=120

# 性能优化配置
MAX_FAILURES=2
MAX_RETRIES=2
CHECK_INTERVAL_HOURS=2

# 日志优化 (减少日志输出)
AUTO_DELETE_ERROR_LOGS_ENABLED=true
AUTO_DELETE_ERROR_LOGS_DAYS=3
AUTO_DELETE_REQUEST_LOGS_ENABLED=true
AUTO_DELETE_REQUEST_LOGS_DAYS=7

# 禁用非必要功能以节省资源
STREAM_OPTIMIZER_ENABLED=false
FAKE_STREAM_ENABLED=false
TOOLS_CODE_EXECUTION_ENABLED=false
URL_NORMALIZATION_ENABLED=false
URL_CONTEXT_ENABLED=false

# 简化模型配置
TEST_MODEL=gemini-1.5-flash
IMAGE_MODELS=[]
SEARCH_MODELS=[]
THINKING_MODELS=[]
