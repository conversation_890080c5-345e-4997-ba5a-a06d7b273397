# GEMINI BALANCE 优化部署方案

## 📋 项目概述
本项目是GEMINI BALANCE的优化部署方案，专为亚马逊免费云服务器设计，具有以下特点：
- 🚀 **简化部署**：一键自动化部署脚本
- 🛡️ **稳定运行**：优化的健康检查和重启策略
- 📊 **资源优化**：适配免费服务器的资源限制
- 🔧 **便捷管理**：提供完整的运维管理脚本

## 📁 文件说明

### 核心配置文件
- `docker-compose.yaml` - 优化的Docker编排配置
- `.env` - 环境变量配置文件
- `deploy-optimized.sh` - 自动化部署脚本
- `manage.sh` - 日常管理脚本

### 文档文件
- `deploy.txt` - 简化部署说明
- `README-部署说明.md` - 详细部署文档

## 🚀 快速部署

### 方法一：自动化部署（推荐）
```bash
# 1. 上传所有文件到服务器
# 2. 给脚本执行权限
chmod +x deploy-optimized.sh
chmod +x manage.sh

# 3. 运行自动部署
./deploy-optimized.sh
```

### 方法二：手动部署
```bash
# 1. 安装Docker
curl -fsSL https://get.docker.com | sudo bash -s docker --mirror Aliyun

# 2. 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 3. 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 4. 启动项目
sudo docker-compose up -d
```

## ⚙️ 配置说明

### 环境变量配置 (.env)
```bash
# 数据库配置
MYSQL_USER=gemini                    # 数据库用户名
MYSQL_PASSWORD=GeminiBalance2024!    # 数据库密码（请修改）
MYSQL_DATABASE=gemini_balance        # 数据库名

# API配置（必须修改）
API_KEYS=["your-gemini-api-key-1","your-gemini-api-key-2"]

# 访问令牌（必须修改）
ALLOWED_TOKENS=["your-secure-token"]
```

**⚠️ 重要：部署前请务必修改以下配置：**
1. `API_KEYS` - 替换为您的真实Gemini API密钥
2. `ALLOWED_TOKENS` - 设置您的安全访问令牌
3. `MYSQL_PASSWORD` - 建议修改为更安全的密码

## 🔧 日常管理

使用管理脚本进行日常运维：

```bash
# 查看服务状态
./manage.sh status

# 启动服务
./manage.sh start

# 停止服务
./manage.sh stop

# 重启服务
./manage.sh restart

# 查看日志
./manage.sh logs

# 更新服务
./manage.sh update

# 备份数据库
./manage.sh backup

# 清理系统
./manage.sh clean
```

## 🛡️ 优化特性

### 1. 资源优化
- MySQL内存限制：128MB缓冲池
- 日志轮转：最大10MB，保留3个文件
- 健康检查优化：减少资源消耗

### 2. 稳定性增强
- 自动重启策略：`unless-stopped`
- 增强的健康检查机制
- 服务依赖管理

### 3. 安全性提升
- 数据库端口不对外暴露
- 环境变量隔离
- 安全的认证配置

## 🌐 访问方式

部署成功后，可通过以下方式访问：
- 本地访问：`http://localhost:8000`
- 外网访问：`http://您的服务器IP:8000`

## 📊 监控和维护

### 健康检查
- 应用健康检查：`/health` 端点
- 数据库健康检查：MySQL ping测试
- 自动故障恢复机制

### 日志管理
- 应用日志：`sudo docker-compose logs gemini-balance`
- 数据库日志：`sudo docker-compose logs mysql`
- 所有日志：`sudo docker-compose logs`

### 数据备份
- 自动备份脚本：`./manage.sh backup`
- 数据卷位置：`mysql_data`
- 建议定期备份数据

## 🔍 故障排除

### 常见问题
1. **服务无法启动**
   ```bash
   # 检查Docker服务
   sudo systemctl status docker
   
   # 查看详细日志
   sudo docker-compose logs
   ```

2. **端口被占用**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep 8000
   
   # 修改docker-compose.yaml中的端口映射
   ```

3. **内存不足**
   ```bash
   # 检查系统资源
   free -h
   df -h
   
   # 清理Docker资源
   ./manage.sh clean
   ```

## 📞 技术支持

如遇到问题，请检查：
1. 服务器系统要求：Ubuntu 18.04+ 或 Amazon Linux 2
2. 最小内存要求：1GB RAM
3. 磁盘空间：至少2GB可用空间
4. 网络要求：能够访问Docker Hub和GitHub

---

**🎉 部署完成后，您的GEMINI BALANCE项目将稳定高效地运行在云服务器上！**
