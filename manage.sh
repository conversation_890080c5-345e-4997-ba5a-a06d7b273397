#!/bin/bash

# GEMINI BALANCE 项目管理脚本
# 简化日常运维操作

set -e

PROJECT_NAME="GEMINI BALANCE"
COMPOSE_FILE="docker-compose.yaml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker未运行，请先启动Docker服务"
        echo "运行: sudo systemctl start docker"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}$PROJECT_NAME 管理脚本${NC}"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  start     - 启动服务"
    echo "  stop      - 停止服务"
    echo "  restart   - 重启服务"
    echo "  status    - 查看服务状态"
    echo "  logs      - 查看日志"
    echo "  update    - 更新服务"
    echo "  backup    - 备份数据库"
    echo "  clean     - 清理未使用的镜像和容器"
    echo "  help      - 显示此帮助信息"
}

# 启动服务
start_service() {
    print_message "启动 $PROJECT_NAME 服务..."
    check_docker
    sudo docker-compose up -d
    print_message "服务启动完成！"
    sleep 5
    show_status
}

# 停止服务
stop_service() {
    print_message "停止 $PROJECT_NAME 服务..."
    sudo docker-compose down
    print_message "服务已停止"
}

# 重启服务
restart_service() {
    print_message "重启 $PROJECT_NAME 服务..."
    sudo docker-compose restart
    print_message "服务重启完成！"
    sleep 5
    show_status
}

# 查看状态
show_status() {
    print_message "服务状态:"
    sudo docker-compose ps
    echo ""
    
    # 检查应用健康状态
    if curl -f http://localhost:8000/health &>/dev/null; then
        print_message "✅ 应用运行正常"
        echo "🌐 访问地址: http://$(curl -s ifconfig.me 2>/dev/null || echo 'YOUR_SERVER_IP'):8000"
    else
        print_warning "⚠️  应用可能未完全启动，请稍后再试"
    fi
}

# 查看日志
show_logs() {
    print_message "显示服务日志 (按 Ctrl+C 退出):"
    sudo docker-compose logs -f --tail=50
}

# 更新服务
update_service() {
    print_message "更新 $PROJECT_NAME 服务..."
    check_docker
    
    print_message "拉取最新镜像..."
    sudo docker-compose pull
    
    print_message "重启服务..."
    sudo docker-compose up -d
    
    print_message "清理旧镜像..."
    sudo docker image prune -f
    
    print_message "更新完成！"
    sleep 5
    show_status
}

# 备份数据库
backup_database() {
    print_message "备份数据库..."
    
    BACKUP_DIR="./backups"
    mkdir -p $BACKUP_DIR
    
    BACKUP_FILE="$BACKUP_DIR/gemini-balance-$(date +%Y%m%d_%H%M%S).sql"
    
    sudo docker-compose exec mysql mysqldump -u root -p${MYSQL_PASSWORD} ${MYSQL_DATABASE} > $BACKUP_FILE
    
    if [ $? -eq 0 ]; then
        print_message "数据库备份完成: $BACKUP_FILE"
    else
        print_error "数据库备份失败"
    fi
}

# 清理系统
clean_system() {
    print_message "清理未使用的Docker资源..."
    
    print_message "清理停止的容器..."
    sudo docker container prune -f
    
    print_message "清理未使用的镜像..."
    sudo docker image prune -f
    
    print_message "清理未使用的网络..."
    sudo docker network prune -f
    
    print_message "清理完成！"
}

# 主逻辑
case "${1:-help}" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    update)
        update_service
        ;;
    backup)
        backup_database
        ;;
    clean)
        clean_system
        ;;
    help|*)
        show_help
        ;;
esac
