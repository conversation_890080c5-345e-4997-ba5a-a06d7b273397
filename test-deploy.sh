#!/bin/bash

# GEMINI BALANCE 轻量级部署测试脚本
# 用于验证一键部署功能

echo "🧪 开始测试轻量级部署..."

# 检查必要文件
echo "📋 检查必要文件..."
REQUIRED_FILES=("docker-compose-lite.yaml" ".env-lite" "deploy-lite.sh" "manage-lite.sh")
MISSING_FILES=()

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -ne 0 ]; then
    echo "❌ 缺少必要文件:"
    printf '   %s\n' "${MISSING_FILES[@]}"
    exit 1
fi

echo "✅ 所有必要文件都存在"

# 检查脚本语法
echo "🔍 检查脚本语法..."
bash -n deploy-lite.sh && echo "✅ deploy-lite.sh 语法正确" || echo "❌ deploy-lite.sh 语法错误"
bash -n manage-lite.sh && echo "✅ manage-lite.sh 语法正确" || echo "❌ manage-lite.sh 语法错误"

# 检查Docker命令
echo "🐳 检查Docker环境..."
if command -v docker &> /dev/null; then
    echo "✅ Docker 已安装: $(docker --version)"
else
    echo "⚠️  Docker 未安装，部署脚本将自动安装"
fi

if docker compose version &> /dev/null; then
    echo "✅ Docker Compose 已安装: $(docker compose version)"
else
    echo "⚠️  Docker Compose 未安装，部署脚本将自动安装"
fi

# 检查端口占用
echo "🔌 检查端口8000..."
if command -v netstat &> /dev/null; then
    if netstat -tlnp 2>/dev/null | grep ":8000 " >/dev/null; then
        echo "⚠️  端口8000已被占用"
        netstat -tlnp | grep ":8000 "
    else
        echo "✅ 端口8000可用"
    fi
else
    echo "⚠️  netstat未安装，无法检查端口占用"
fi

# 检查磁盘空间
echo "💾 检查磁盘空间..."
AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
if [ "$AVAILABLE_SPACE" -gt 1048576 ]; then  # 1GB = 1048576 KB
    echo "✅ 磁盘空间充足: $(df -h . | tail -1 | awk '{print $4}') 可用"
else
    echo "⚠️  磁盘空间不足1GB，可能影响部署"
fi

# 检查内存
echo "🧠 检查内存..."
TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
AVAILABLE_MEM=$(free -m | awk 'NR==2{printf "%.0f", $7}')
echo "   总内存: ${TOTAL_MEM}MB, 可用内存: ${AVAILABLE_MEM}MB"

if [ "$AVAILABLE_MEM" -lt 256 ]; then
    echo "⚠️  可用内存不足256MB，可能影响性能"
else
    echo "✅ 内存充足"
fi

echo ""
echo "🎯 测试总结:"
echo "   ✅ 文件检查完成"
echo "   ✅ 语法检查完成"
echo "   ✅ 环境检查完成"
echo ""
echo "🚀 现在可以运行部署脚本:"
echo "   chmod +x deploy-lite.sh manage-lite.sh"
echo "   ./deploy-lite.sh"
echo ""
echo "📝 部署后可用命令:"
echo "   ./manage-lite.sh status   # 查看状态"
echo "   ./manage-lite.sh logs     # 查看日志"
echo "   ./manage-lite.sh stats    # 查看资源使用"
