#!/bin/bash

# GEMINI BALANCE 优化部署脚本
# 适用于亚马逊免费云服务器 (Ubuntu/Amazon Linux)

set -e  # 遇到错误立即退出

echo "🚀 开始部署 GEMINI BALANCE 项目..."

# 检查系统类型
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    echo "检测到系统: $OS"
fi

# 更新系统包
echo "📦 更新系统包..."
if command -v apt-get &> /dev/null; then
    sudo apt-get update -y
    sudo apt-get install -y curl wget git
elif command -v yum &> /dev/null; then
    sudo yum update -y
    sudo yum install -y curl wget git
fi

# 安装 Docker (如果未安装)
if ! command -v docker &> /dev/null; then
    echo "🐳 安装 Docker..."
    curl -fsSL https://get.docker.com | sudo bash -s docker --mirror Aliyun
    
    # 启动 Docker 服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 将当前用户添加到 docker 组 (可选，避免每次使用 sudo)
    sudo usermod -aG docker $USER
    echo "⚠️  请注意：需要重新登录才能生效 docker 组权限"
else
    echo "✅ Docker 已安装"
fi

# 安装 Docker Compose (如果未安装)
if ! command -v docker-compose &> /dev/null; then
    echo "🔧 安装 Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
else
    echo "✅ Docker Compose 已安装"
fi

# 确保 Docker 服务运行
echo "🔄 启动 Docker 服务..."
sudo systemctl start docker

# 创建项目目录
PROJECT_DIR="/opt/gemini-balance"
echo "📁 创建项目目录: $PROJECT_DIR"
sudo mkdir -p $PROJECT_DIR
sudo chown $USER:$USER $PROJECT_DIR

# 复制配置文件到项目目录
echo "📋 复制配置文件..."
cp docker-compose.yaml $PROJECT_DIR/
cp .env $PROJECT_DIR/

# 进入项目目录
cd $PROJECT_DIR

# 拉取最新镜像
echo "⬇️  拉取最新镜像..."
sudo docker-compose pull

# 停止旧容器 (如果存在)
echo "🛑 停止旧容器..."
sudo docker-compose down --remove-orphans

# 启动服务
echo "🚀 启动服务..."
sudo docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
sudo docker-compose ps

# 检查端口开放情况
echo "🔍 检查端口开放情况..."
if command -v ufw >/dev/null 2>&1 && ufw status | grep -q "Status: active"; then
    if ! ufw status | grep -q "8000"; then
        echo "⚠️  检测到UFW防火墙，正在开放8000端口..."
        sudo ufw allow 8000
    fi
fi

# 检查健康状态
echo "❤️  检查应用健康状态..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health &>/dev/null; then
        echo "✅ 应用启动成功！"
        PUBLIC_IP=$(curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP")
        echo "🌐 访问地址: http://$PUBLIC_IP:8000"
        break
    else
        echo "等待应用启动... ($i/10)"
        sleep 10
    fi
done

# 显示日志 (最后20行)
echo "📋 最近日志:"
sudo docker-compose logs --tail=20

echo "🎉 部署完成！"
echo ""
echo "📝 常用命令:"
echo "  查看状态: sudo docker-compose ps"
echo "  查看日志: sudo docker-compose logs -f"
echo "  重启服务: sudo docker-compose restart"
echo "  停止服务: sudo docker-compose down"
echo "  更新服务: sudo docker-compose pull && sudo docker-compose up -d"
echo ""
echo "🔧 端口检查:"
echo "  如果无法访问服务，请运行: chmod +x check-port.sh && ./check-port.sh"
echo "  这将帮助您检查和配置端口开放设置"
