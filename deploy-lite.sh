#!/bin/bash

# GEMINI BALANCE 轻量级部署脚本
# 适用于资源受限的服务器 (1GB内存以下)
# 使用SQLite数据库，无需MySQL，大幅减少资源占用

set -e

echo "🚀 开始轻量级部署 GEMINI BALANCE..."

# 检查系统资源
echo "📊 检查系统资源..."
TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
AVAILABLE_MEM=$(free -m | awk 'NR==2{printf "%.0f", $7}')
echo "总内存: ${TOTAL_MEM}MB, 可用内存: ${AVAILABLE_MEM}MB"

if [ "$TOTAL_MEM" -lt 512 ]; then
    echo "⚠️  警告: 系统内存不足512MB，可能影响性能"
fi

# 更新系统包 (仅必要的)
echo "📦 安装必要软件包..."
if command -v apt-get &> /dev/null; then
    sudo apt-get update -qq
    sudo apt-get install -y -qq curl wget
elif command -v yum &> /dev/null; then
    sudo yum install -y -q curl wget
fi

# 安装 Docker (如果未安装)
if ! command -v docker &> /dev/null; then
    echo "🐳 安装 Docker..."
    curl -fsSL https://get.docker.com | sudo bash -s docker --mirror Aliyun
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -aG docker $USER
    echo "✅ Docker 安装完成"
else
    echo "✅ Docker 已安装"
fi

# 安装 Docker Compose 插件 (如果未安装)
if ! docker compose version &> /dev/null; then
    echo "🔧 安装 Docker Compose 插件..."
    sudo apt update -qq
    sudo apt install -y docker-compose-plugin
    echo "✅ Docker Compose 插件安装完成"
else
    echo "✅ Docker Compose 已安装"
fi

# 安装网络工具 (如果未安装)
if ! command -v netstat &> /dev/null; then
    echo "🌐 安装网络工具..."
    sudo apt install -y net-tools
    echo "✅ 网络工具安装完成"
else
    echo "✅ 网络工具已安装"
fi

# 确保 Docker 服务运行
sudo systemctl start docker

# 创建数据目录
echo "📁 创建数据目录..."
mkdir -p ./data

# 使用轻量级配置
echo "📋 使用轻量级配置..."
if [ ! -f .env ]; then
    cp .env-lite .env
    echo "✅ 已创建轻量级环境配置文件"
else
    echo "⚠️  .env文件已存在，如需使用轻量级配置请手动替换"
fi

# 检查端口
echo "🔍 检查端口8000..."
if netstat -tlnp 2>/dev/null | grep ":8000 " >/dev/null; then
    echo "⚠️  端口8000已被占用，请先停止占用进程"
    netstat -tlnp | grep ":8000 "
    exit 1
fi

# 自动开放防火墙端口
if command -v ufw >/dev/null 2>&1 && ufw status | grep -q "Status: active"; then
    if ! ufw status | grep -q "8000"; then
        echo "🔓 开放防火墙端口8000..."
        sudo ufw allow 8000
    fi
fi

# 拉取镜像
echo "⬇️  拉取应用镜像..."
sudo docker pull ghcr.io/snailyp/gemini-balance:latest

# 停止旧容器
echo "🛑 清理旧容器..."
sudo docker stop gemini-balance-lite 2>/dev/null || true
sudo docker rm gemini-balance-lite 2>/dev/null || true

# 启动轻量级服务
echo "🚀 启动轻量级服务..."
sudo docker compose -f docker-compose-lite.yaml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 20

# 检查服务状态
echo "🔍 检查服务状态..."
sudo docker compose -f docker-compose-lite.yaml ps

# 检查健康状态
echo "❤️  检查应用健康状态..."
for i in {1..6}; do
    if curl -f http://localhost:8000/health &>/dev/null; then
        echo "✅ 轻量级部署成功！"
        PUBLIC_IP=$(curl -s --connect-timeout 5 ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP")
        echo "🌐 访问地址: http://$PUBLIC_IP:8000"
        break
    else
        echo "等待应用启动... ($i/6)"
        sleep 10
    fi
done

# 显示资源使用情况
echo "📊 当前资源使用:"
sudo docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

echo ""
echo "🎉 轻量级部署完成！"
echo ""
echo "💡 轻量级特性:"
echo "  ✓ 使用SQLite数据库 (无需MySQL)"
echo "  ✓ 内存限制512MB"
echo "  ✓ 减少日志输出"
echo "  ✓ 禁用非必要功能"
echo "  ✓ 优化健康检查频率"
echo ""
echo "📝 管理命令:"
echo "  使用管理脚本: ./manage-lite-fixed.sh [命令]"
echo "  查看状态: sudo docker compose -f docker-compose-lite.yaml ps"
echo "  查看日志: sudo docker compose -f docker-compose-lite.yaml logs -f"
echo "  重启服务: sudo docker compose -f docker-compose-lite.yaml restart"
echo "  停止服务: sudo docker compose -f docker-compose-lite.yaml down"
echo "  更新服务: sudo docker pull ghcr.io/snailyp/gemini-balance:latest && sudo docker compose -f docker-compose-lite.yaml up -d"
echo ""
echo "💾 数据存储: ./data/gemini_balance.db (SQLite数据库文件)"
echo ""
echo "🔧 正在创建修复版管理脚本..."

# 确保管理脚本使用正确的Docker Compose语法
if [ -f "manage-lite.sh" ]; then
    chmod +x manage-lite.sh
    echo "✅ 管理脚本已修复并可用"
    echo "   使用方法: ./manage-lite.sh [status|start|stop|restart|logs|stats|backup|clean|update]"
else
    echo "⚠️  管理脚本不存在，请手动上传 manage-lite.sh 文件"
fi
