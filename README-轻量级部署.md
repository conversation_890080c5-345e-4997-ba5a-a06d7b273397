# GEMINI BALANCE 轻量级部署方案

## 🎯 轻量级特性

### 📊 资源对比

| 项目 | 标准部署 | 轻量级部署 | 节省 |
|------|----------|------------|------|
| **内存占用** | ~850MB | ~300MB | **65%** |
| **磁盘占用** | ~1GB | ~400MB | **60%** |
| **启动时间** | ~60秒 | ~30秒 | **50%** |
| **容器数量** | 2个 | 1个 | **50%** |

### ✨ 优化措施

1. **数据库优化**
   - ❌ MySQL 8.0 (~550MB)
   - ✅ SQLite (~几MB)

2. **资源限制**
   - 内存限制: 512MB
   - CPU限制: 0.5核心
   - 日志限制: 5MB × 2个文件

3. **功能精简**
   - 禁用流优化器
   - 禁用代码执行工具
   - 禁用URL上下文理解
   - 减少健康检查频率

4. **日志优化**
   - 日志级别: WARNING
   - 自动清理: 3天错误日志，7天请求日志

## 🚀 快速部署

### 一键部署（推荐）
```bash
# 1. 给脚本执行权限
chmod +x deploy-lite.sh manage-lite.sh

# 2. 运行轻量级部署
./deploy-lite.sh
```

### 手动部署
```bash
# 1. 安装Docker
curl -fsSL https://get.docker.com | sudo bash

# 2. 启动服务
sudo docker-compose -f docker-compose-lite.yaml up -d
```

## ⚙️ 配置文件

### 轻量级配置 (.env-lite)
```bash
# 使用SQLite数据库
DATABASE_TYPE=sqlite
SQLITE_DATABASE=data/gemini_balance.db

# 性能优化
LOG_LEVEL=WARNING
MAX_FAILURES=2
MAX_RETRIES=2
TIME_OUT=120

# 禁用非必要功能
STREAM_OPTIMIZER_ENABLED=false
FAKE_STREAM_ENABLED=false
TOOLS_CODE_EXECUTION_ENABLED=false
```

### Docker配置 (docker-compose-lite.yaml)
```yaml
services:
  gemini-balance:
    image: ghcr.io/snailyp/gemini-balance:latest
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
```

## 🔧 管理命令

```bash
# 服务管理
./manage-lite.sh start      # 启动服务
./manage-lite.sh stop       # 停止服务
./manage-lite.sh restart    # 重启服务
./manage-lite.sh status     # 查看状态

# 监控和维护
./manage-lite.sh stats      # 查看资源使用
./manage-lite.sh logs       # 查看日志
./manage-lite.sh backup     # 备份数据库
./manage-lite.sh clean      # 清理资源
./manage-lite.sh update     # 更新服务
```

## 📊 适用场景

### ✅ 适合轻量级部署的情况
- 服务器内存 ≤ 1GB
- 个人使用或小团队
- API调用量不大
- 对高级功能需求不高
- 希望快速部署和低维护成本

### ❌ 不适合轻量级部署的情况
- 高并发访问需求
- 需要复杂的数据分析
- 需要图像生成功能
- 需要代码执行功能
- 企业级部署需求

## 🛡️ 性能优化建议

### 系统级优化
```bash
# 1. 启用内存交换 (如果内存不足)
sudo fallocate -l 1G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 2. 优化Docker日志
echo '{"log-driver":"json-file","log-opts":{"max-size":"10m","max-file":"3"}}' | sudo tee /etc/docker/daemon.json
sudo systemctl restart docker
```

### 应用级优化
```bash
# 1. 定期清理资源
./manage-lite.sh clean

# 2. 定期备份数据
./manage-lite.sh backup

# 3. 监控资源使用
./manage-lite.sh stats
```

## 🔍 故障排除

### 内存不足
```bash
# 检查内存使用
free -h
./manage-lite.sh stats

# 重启服务释放内存
./manage-lite.sh restart
```

### 磁盘空间不足
```bash
# 检查磁盘使用
df -h

# 清理Docker资源
./manage-lite.sh clean

# 清理系统日志
sudo journalctl --vacuum-time=7d
```

### 服务无响应
```bash
# 查看服务状态
./manage-lite.sh status

# 查看详细日志
./manage-lite.sh logs

# 重启服务
./manage-lite.sh restart
```

## 📈 升级路径

如果后续需要更多功能，可以升级到标准部署：

```bash
# 1. 备份轻量级数据
./manage-lite.sh backup

# 2. 停止轻量级服务
./manage-lite.sh stop

# 3. 切换到标准部署
cp .env-lite .env-backup
cp .env-standard .env  # 如果有标准配置
./deploy-optimized.sh
```

## 💡 最佳实践

1. **定期维护**
   - 每周运行一次 `./manage-lite.sh clean`
   - 每月备份一次数据库
   - 监控资源使用情况

2. **安全建议**
   - 定期更新镜像
   - 使用强密码作为访问令牌
   - 限制访问IP（如果可能）

3. **性能监控**
   - 使用 `./manage-lite.sh stats` 监控资源
   - 关注内存使用率，避免超过80%
   - 定期检查日志文件大小

---

**🎉 轻量级部署让您在资源受限的环境中也能稳定运行GEMINI BALANCE！**
